import React from "react";
import ReactDOM from "react-dom/client";
import App from "./App.tsx";
import "./index.css";
import { AuthProvider } from "./contexts/AuthContext.tsx";
import { PicksProvider } from "./contexts/PicksContext.tsx";

ReactDOM.createRoot(document.getElementById("root") as HTMLElement).render(
  <React.StrictMode>
    <AuthProvider>
      <PicksProvider>
        <App />
      </PicksProvider>
    </AuthProvider>
  </React.StrictMode>
);
