import React from "react";
import {
  HiBars3,
  HiMagnifyingGlass,
  HiAdjustmentsHorizontal,
  HiListBullet,
  HiArrowLeft,
} from "react-icons/hi2";
import { usePicks } from "../../contexts/PicksContext";

interface HeaderProps {
  onBackToHome: () => void;
}

function Header({ onBackToHome }: HeaderProps) {
  const { totalPicksCount } = usePicks();

  return (
    <header className="w-full bg-[#061844] p-4 border-b border-gray-700">
      <div className="max-w-7xl mx-auto flex items-center justify-center gap-2 sm:gap-4">
        <button
            onClick={onBackToHome}
            className="flex items-center gap-2 px-4 py-2.5 bg-[#233e6c] hover:bg-[#1a2d54] text-white hover:text-gray-100 rounded-lg transition-all duration-200 shadow-sm font-medium hover:cursor-pointer"
          >
            <HiArrowLeft className="w-4 h-4" />
            <span className="text-sm hidden lg:inline">Back to Parlay Engine</span>
            <span className="text-sm lg:hidden">Back</span>
          </button>
        <HiBars3 className="w-8 h-8 text-white bg-[#233e6c] rounded p-1 cursor-pointer hover:bg-[#1a2d54] hover:text-gray-100 transition-colors flex-shrink-0 " />


        <img
          src="/project_parlay_logo.png"
          alt="Project Parlay Logo"
          className="h-10 sm:h-12 w-auto select-none flex-shrink-0 hover:cursor-pointer"
          onClick={onBackToHome}
          draggable="false"
        />

        <div className="flex-1 relative min-w-0 max-w-md sm:max-w-lg lg:max-w-xl">
          <HiMagnifyingGlass className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-white pointer-events-none" />
          <input
            type="text"
            placeholder="Search for handicappers, games, sports, teams..."
            className="w-full bg-[#233e6c] text-white placeholder-gray-400 rounded-lg py-4 pl-10 pr-20 focus:outline-none focus:ring-2 focus:ring-[#58C612] text-sm cursor-text"
          />
          <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center gap-2">
            <div className="h-6 w-px bg-gray-600"></div>
            <button className="flex items-center gap-1 text-white hover:text-gray-100 hover:cursor-pointer transition-colors">
              <span className="text-sm hidden sm:inline">Filter</span>
              <HiAdjustmentsHorizontal className="w-5 h-5" />
            </button>
          </div>
        </div>

        <div className="relative flex-shrink-0">
          <HiListBullet className="w-8 h-8 text-white bg-[#233e6c] rounded p-1 cursor-pointer hover:bg-[#1a2d54] hover:text-gray-100 transition-colors" />
          {totalPicksCount > 0 && (
            <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs font-bold rounded-full min-w-[18px] h-[18px] flex items-center justify-center px-1 shadow-lg border-2 border-[#061844]">
              {totalPicksCount > 99 ? '99+' : totalPicksCount}
            </div>
          )}
        </div>
      </div>
    </header>
  );
}

export default Header;
