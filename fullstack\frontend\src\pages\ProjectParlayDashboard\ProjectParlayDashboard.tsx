import React, { useState, useEffect, useCallback } from "react";
import PickForm from "../../components/ProjectParlayDashboard/PickForm";
import BoostForm from "../../components/ProjectParlayDashboard/BoostForm";
import ProtectedForm from "../../components/ProjectParlayDashboard/ProtectedForm";
import { getConfidenceColor } from "../../utils/colorUtils";

// Add global styles to hide number input spinners
const globalStyles = `
  /* Hide number input spinners */
  input[type=number]::-webkit-inner-spin-button, 
  input[type=number]::-webkit-outer-spin-button { 
    -webkit-appearance: none; 
    margin: 0; 
  }
  input[type=number] {
    -moz-appearance: textfield;
  }
`;

// --- Constants ---
const STAT_TYPE_OPTIONS: Record<string, string[]> = {
  MLB: [
    "K's",
    "Total Bases",
    "Hitter Fpts",
    "Pitcher Fpts",
    "H+R+RBIs",
    "HR's",
    "Hits Allowed",
    "S<PERSON>'s",
    "Doubles",
    "Walks Allowed",
    "Singles",
    "Walks",
    "Hits",
    "Earned Runs Allowed",
    "RBI's",
    "Runs",
  ],
  NBA: [
    "Points",
    "PRA",
    "Rebounds",
    "Assists",
    "3-pt's",
    "P+A",
    "FG Made",
    "D Rebounds",
    "Fpts",
    "R+A",
    "O Rebounds",
    "3-pt's Attempted",
    "FT's Made",
    "FG Attempted",
    "P+R",
    "Dunks",
    "Blocks",
    "Steals",
    "B+S",
    "Turnovers",
  ],
  NHL: [
    "Goals",
    "Assists",
    "Points",
    "Shots on Goal",
    "Power Play Points",
    "Hits",
    "Blocked Shots",
    "Penalty Minutes",
    "Faceoff Wins",
    "Time on Ice",
    "Plus/Minus",
    "Shots",
    "Power Play Goals",
    "Shorthanded Goals",
    "Game-Winning Goals",
    "Overtime Goals",
    "First Goal",
    "Last Goal",
    "Anytime Goal",
    "Multi-Point Game",
    "Multi-Goal Game",
  ],
  NFL: [
    "Passing Yards",
    "Passing Touchdowns",
    "Interceptions Thrown",
    "Completions",
    "Pass Attempts",
    "Rushing Yards",
    "Rushing Touchdowns",
    "Carries",
    "Receiving Yards",
    "Receptions",
    "Receiving Touchdowns",
    "Targets",
    "Longest Reception",
    "Longest Rush",
    "Total Touchdowns",
    "Anytime Touchdown",
    "First Touchdown",
    "Last Touchdown",
    "Field Goals Made",
    "Field Goals Attempted",
    "Extra Points Made",
    "Tackles",
    "Sacks",
    "Interceptions",
    "Passes Defended",
    "Fumbles Recovered",
    "Forced Fumbles",
    "Defensive Touchdowns",
  ],
};

const LEAGUES = ["MLB", "NBA", "NFL", "NHL"];
const PICK_ORIGINS = [
  "ChalkBoardPI",
  "HarryLock",
  "DanGamblePOD",
  "DanGambleAIEdge",
  "GameScript",
  "Winible",
  "DoberMan",
  "JoshMiller",
  "Me",
];

// --- Types ---
export interface OriginData {
  name: string;
  confidence: number | null;
  prediction: number; // 0 for Lower, 1 for Higher
}

export interface Pick {
  id: string | number;
  pID?: string | number; // From optimizer response, could be the same as id
  name: string;
  odds: string;
  pick_origin: OriginData[]; // MVP uses array of objects with name, confidence, prediction
  league: string[]; // Should be a single string if exclusive, or array if multiple allowed (MVP implies exclusive)
  reusable: boolean;
  capital_limit: number;
  mutual_exclusion: number; // -1 for None
  pick_type: "MoneyLine" | "Prop" | string; // Enforce if possible
  player_team?: string;
  stat_type?: string; // e.g., "K's", "Points"
  prediction: number; // Global prediction for the pick (0 for Lower, 1 for Higher)
  confidence?: number; // Overall score/confidence from backend processing or optimizer
  bayesian_prob?: string;
  logistic_prob?: string;
  bayesian_conf?: number; // Weight for Bayesian model in blend
  actual_result?: number; // 0 for Loss, 1 for Win (for verification)
  is_verified_temp_state?: number; // For UI interaction before submitting
}

interface SubparlayPick {
  pID: string | number;
  odds: string; // MVP had this, might be useful
  confidence?: number;
  // other pick properties if needed by displaySubparlays
}
interface SubparlayColumn extends Array<SubparlayPick> {}

interface OptimizerData {
  best_config: string;
  best_score: number;
  sorted_picks: Pick[]; // Full pick objects
  split_index?: number;
  subparlays: SubparlayColumn[];
}

interface BoostPromoData {
  boost_percentage: number;
  required_picks: number;
  same_sport: boolean;
}

interface ProtectedPromoData {
  protected_amount: number;
  eligible_leagues: string[];
}

function ProjectParlayDashboard() {
  const [userAccuracy, setUserAccuracy] = useState<string>("");
  const [fetchedAccuracy, setFetchedAccuracy] = useState<string>("N/A");
  const [totalCapital, setTotalCapital] = useState<string>("");

  const [pickNameInput, setPickNameInput] = useState<string>("");
  const [tempPickName, setTempPickName] = useState<string>("");

  const [picks, setPicks] = useState<Pick[]>([]);
  const [subparlays, setSubparlays] = useState<SubparlayColumn[]>([]);
  const [optimizerResultSummary, setOptimizerResultSummary] =
    useState<string>("");
  const [optimizerSplitIndex, setOptimizerSplitIndex] = useState<
    number | undefined
  >(undefined);
  const [serverResponse, setServerResponse] = useState<string>("");

  const [showPickForm, setShowPickForm] = useState<boolean>(false);
  const [showBoostForm, setShowBoostForm] = useState<boolean>(false);
  const [showProtectedForm, setShowProtectedForm] = useState<boolean>(false);
  const [editingPick, setEditingPick] = useState<Pick | null>(null);

  const API_BASE_URL = "/api";

  const fetchPicks = useCallback(async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/get_picks`);
      if (!response.ok)
        throw new Error(`HTTP error! status: ${response.status}`);
      const data = await response.json();

      // Process picks similar to handleLoadSamplePicks to ensure unique IDs
      const existingIds = new Set<string | number>();
      const processedPicks = (data.objects || []).map(
        (p: Pick, index: number) => {
          let uniqueId = p.id;
          // Generate a unique ID if none exists or if it's a duplicate
          if (
            uniqueId === undefined ||
            uniqueId === null ||
            existingIds.has(uniqueId)
          ) {
            uniqueId = `generated_${Date.now()}_${index}`;
          }
          existingIds.add(uniqueId);

          // Return properly structured pick with unique ID
          return {
            ...p,
            id: uniqueId,
            // Clone the pick to ensure it's a new object reference
            is_verified_temp_state:
              p.actual_result !== undefined ? p.actual_result : 0,
          };
        }
      );

      console.log("Fetched picks with unique IDs:", processedPicks);
      setPicks(processedPicks);
      setServerResponse(data.message || "Picks loaded.");
    } catch (error) {
      console.error("Error fetching picks:", error);
      setServerResponse(
        `Error fetching picks: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }, []);

  const fetchAccuracy = useCallback(async () => {
    // Placeholder: In a real app, you'd fetch this.
    // For now, assume it's part of a user profile or fetched separately.
    // const response = await fetch(`${API_BASE_URL}/get_user_accuracy`);
    // const data = await response.json();
    // setFetchedAccuracy(data.accuracy);
  }, []);

  useEffect(() => {
    fetchPicks();
    fetchAccuracy();
    return undefined;
  }, [fetchPicks, fetchAccuracy]);

  const handleUpdateAccuracy = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/update_accuracy`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ accuracy: userAccuracy }),
      });
      if (!response.ok)
        throw new Error(`HTTP error! status: ${response.status}`);
      const data = await response.json();
      setFetchedAccuracy(data.user_accuracy);
      setServerResponse(data.message || "Accuracy updated.");
      setUserAccuracy(""); // Clear input
    } catch (error) {
      console.error("Error updating accuracy:", error);
      setServerResponse(
        `Error updating accuracy: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  };

  const handleOpenPickForm = () => {
    if (!pickNameInput.trim()) {
      alert("Please enter a pick name first.");
      return;
    }
    setTempPickName(pickNameInput);
    setEditingPick(null);
    setShowPickForm(true);
  };
  const handleClosePickForm = () => {
    setShowPickForm(false);
    setEditingPick(null);
    setTempPickName(""); // Clear temp name used by form
  };

  const handleSubmitPickForm = async (
    formData: Omit<
      Pick,
      | "id"
      | "pID"
      | "confidence"
      | "bayesian_prob"
      | "logistic_prob"
      | "bayesian_conf"
    >
  ) => {
    // Client-side validation before API call
    const missingFields: string[] = [];
    if (!formData.name) missingFields.push("Pick Name");
    if (!formData.odds) missingFields.push("Odds");
    if (!formData.pick_origin || formData.pick_origin.length === 0)
      missingFields.push("Pick Origin");
    if (!formData.league || formData.league.length === 0)
      missingFields.push("League");
    if (formData.pick_type === "Prop") {
      if (!formData.player_team) missingFields.push("Player Team");
      if (!formData.stat_type) missingFields.push("Stat Type");
    }
    if (missingFields.length > 0) {
      alert(`Please complete all required fields: ${missingFields.join(", ")}`);
      return;
    }
    // Determine endpoint and payload
    const payload = editingPick
      ? { ...formData, id: editingPick.id }
      : formData;
    const endpoint = editingPick
      ? `${API_BASE_URL}/edit`
      : `${API_BASE_URL}/process`;
    try {
      const response = await fetch(endpoint, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      });
      const data = await response.json();
      if (!response.ok) {
        const msg = data.response || `Server error: ${response.status}`;
        alert(msg);
        setServerResponse(`Error submitting pick: ${msg}`);
        return;
      }
      // Process picks similar to fetchPicks to ensure unique IDs
      const existingIds = new Set<string | number>();
      const processedPicks = (data.objects || []).map(
        (p: Pick, index: number) => {
          let uniqueId = p.id;
          if (
            uniqueId === undefined ||
            uniqueId === null ||
            existingIds.has(uniqueId)
          ) {
            uniqueId = `form_${Date.now()}_${index}`;
          }
          existingIds.add(uniqueId);

          return {
            ...p,
            id: uniqueId,
            is_verified_temp_state:
              p.actual_result !== undefined ? p.actual_result : 0,
          };
        }
      );

      console.log("Picks after form submission:", processedPicks);
      setPicks(processedPicks);
      const successMsg =
        data.response || (editingPick ? "Pick updated." : "Pick created.");
      setServerResponse(successMsg);
      handleClosePickForm();
      if (!editingPick) setPickNameInput("");
    } catch (err) {
      const errMsg = err instanceof Error ? err.message : String(err);
      console.error("Error submitting pick form:", err);
      alert(`Error submitting pick: ${errMsg}`);
      setServerResponse(`Error submitting pick: ${errMsg}`);
    }
  };

  const handleEditPick = (pickToEdit: Pick) => {
    setPickNameInput(pickToEdit.name); // Pre-fill main input for context if desired, or just use tempPickName
    setTempPickName(pickToEdit.name);
    setEditingPick(pickToEdit);
    setShowPickForm(true);
  };

  const handleDeletePick = async (pickId: string | number) => {
    if (!confirm(`Are you sure you want to delete pick ID: ${pickId}?`)) return;
    try {
      const response = await fetch(`${API_BASE_URL}/delete`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ id: pickId }),
      });
      if (!response.ok)
        throw new Error(`HTTP error! status: ${response.status}`);
      const data = await response.json();
      // Process picks consistently to ensure unique IDs
      const existingIds = new Set<string | number>();
      const processedPicks = (data.objects || []).map(
        (p: Pick, index: number) => {
          let uniqueId = p.id;
          if (
            uniqueId === undefined ||
            uniqueId === null ||
            existingIds.has(uniqueId)
          ) {
            uniqueId = `delete_${Date.now()}_${index}`;
          }
          existingIds.add(uniqueId);

          return {
            ...p,
            id: uniqueId,
            is_verified_temp_state:
              p.actual_result !== undefined ? p.actual_result : 0,
          };
        }
      );

      console.log("Picks after deletion:", processedPicks);
      setPicks(processedPicks);
      setServerResponse(data.message || "Pick deleted.");
    } catch (error) {
      console.error("Error deleting pick:", error);
      setServerResponse(
        `Error deleting pick: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  };

  const handleOpenBoostForm = () => setShowBoostForm(true);
  const handleCloseBoostForm = () => setShowBoostForm(false);
  const handleSubmitBoostForm = async (formData: BoostPromoData) => {
    try {
      const response = await fetch(`${API_BASE_URL}/create_boost_promo`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(formData),
      });
      if (!response.ok)
        throw new Error(`HTTP error! status: ${response.status}`);
      const data = await response.json();
      setServerResponse(data.response || "Boost promo created.");
      handleCloseBoostForm();
    } catch (error) {
      console.error("Error submitting boost form:", error);
      setServerResponse(
        `Error creating boost promo: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  };

  const handleOpenProtectedForm = () => setShowProtectedForm(true);
  const handleCloseProtectedForm = () => setShowProtectedForm(false);
  const handleSubmitProtectedForm = async (formData: ProtectedPromoData) => {
    try {
      const response = await fetch(`${API_BASE_URL}/create_protected_promo`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(formData),
      });
      if (!response.ok)
        throw new Error(`HTTP error! status: ${response.status}`);
      const data = await response.json();
      setServerResponse(data.response || "Protected promo created.");
      handleCloseProtectedForm();
    } catch (error) {
      console.error("Error submitting protected form:", error);
      setServerResponse(
        `Error creating protected promo: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  };

  const handleRunOptimizer = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/optimize_split`);
      if (!response.ok)
        throw new Error(`HTTP error! status: ${response.status}`);
      const data: OptimizerData = await response.json();
      setOptimizerResultSummary(
        `${data.best_config} | Score: ${data.best_score}`
      );
      // The optimizer might return a different set of 'sorted_picks' not directly mapping to current 'picks'
      // For display purposes, if it returns full pick objects, use them.
      // Or, if it only returns IDs, you might need to re-sort your existing 'picks' state.
      // For now, assuming it returns full objects for the 'sorted_picks' display area.
      // Process optimizer picks consistently
      const existingIds = new Set<string | number>();
      const processedPicks = (data.sorted_picks || []).map(
        (p: Pick, index: number) => {
          let uniqueId = p.id;
          if (
            uniqueId === undefined ||
            uniqueId === null ||
            existingIds.has(uniqueId)
          ) {
            uniqueId = `optimizer_${Date.now()}_${index}`;
          }
          existingIds.add(uniqueId);

          return {
            ...p,
            id: uniqueId,
            is_verified_temp_state:
              p.actual_result !== undefined ? p.actual_result : 0,
          };
        }
      );

      console.log("Picks after optimizer run:", processedPicks);
      setPicks(processedPicks);
      setSubparlays(data.subparlays || []);
      setOptimizerSplitIndex(data.split_index);
      setServerResponse("Optimizer run successfully."); // MVP had data.message, adjust if needed
    } catch (error) {
      console.error("Error running optimizer:", error);
      setServerResponse(
        `Error running optimizer: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  };

  const handleLoadSamplePicks = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/load_sample_picks`, {
        method: "POST",
      });
      if (!response.ok)
        throw new Error(`HTTP error! status: ${response.status}`);
      const data = await response.json();
      const existingIds = new Set<string | number>();
      const processedPicks = (data.objects || []).map(
        (p: Pick, index: number) => {
          let uniqueId = p.id;
          if (
            uniqueId === undefined ||
            uniqueId === null ||
            existingIds.has(uniqueId)
          ) {
            uniqueId = `sample_${Date.now()}_${index}`;
          }
          existingIds.add(uniqueId);
          return {
            ...p,
            id: uniqueId,
            is_verified_temp_state:
              p.actual_result !== undefined ? p.actual_result : 0,
          };
        }
      );
      setPicks(processedPicks);
      setSubparlays([]);
      setOptimizerResultSummary("");
      setOptimizerSplitIndex(undefined);
      setServerResponse(data.message || "Sample picks loaded.");
    } catch (error) {
      console.error("Error loading sample picks:", error);
      setServerResponse(
        `Error loading samples: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  };

  const handleClearPicks = async () => {
    if (!confirm("Are you sure you want to clear all picks?")) return;
    try {
      const response = await fetch(`${API_BASE_URL}/clear_picks`, {
        method: "POST",
      });
      if (!response.ok)
        throw new Error(`HTTP error! status: ${response.status}`);
      const data = await response.json();
      setPicks([]);
      setSubparlays([]);
      setOptimizerResultSummary("");
      setOptimizerSplitIndex(undefined);
      setServerResponse(data.message || "All picks cleared.");
    } catch (error) {
      console.error("Error clearing picks:", error);
      setServerResponse(
        `Error clearing picks: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  };

  const togglePickVerification = (pickId: string | number) => {
    console.log(`Toggling verification for pick ID: ${pickId}`);

    setPicks((prevPicks) => {
      // Create a new array with all items unchanged except the one matching pickId
      return prevPicks.map((pick) => {
        // Skip picks that don't match the ID
        if (pick.id !== pickId) {
          return pick; // Return unchanged pick
        }

        // For the matching pick, toggle between 0 and 1 only
        const currentVerification = pick.is_verified_temp_state;
        // Simply toggle between 0 and 1
        const nextState = currentVerification === 0 ? 1 : 0;

        console.log(
          `Pick ${pick.id}: Changing state from ${currentVerification} to ${nextState}`
        );

        // Create a new pick object with the updated state
        return {
          ...pick,
          is_verified_temp_state: nextState,
        };
      });
    });
  };

  const handleSubmitVerifiedPicks = async () => {
    const verifiedPayload = picks
      .filter((p) => p.is_verified_temp_state !== undefined)
      .map((p) => ({
        id: p.id,
        actual_result: p.is_verified_temp_state as number,
      }));

    if (verifiedPayload.length === 0) {
      setServerResponse("No picks marked for verification.");
      return;
    }

    try {
      const response = await fetch(`${API_BASE_URL}/submit_verified`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ verified: verifiedPayload }),
      });
      if (!response.ok)
        throw new Error(`HTTP error! status: ${response.status}`);
      const data = await response.json();
      setServerResponse(data.message || "Verification submitted.");
      // Optionally re-fetch picks or update accuracy based on backend response
      fetchPicks();
      if (data.user_accuracy) setFetchedAccuracy(data.user_accuracy);
    } catch (error) {
      console.error("Error submitting verified picks:", error);
      setServerResponse(
        `Error submitting verification: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  };

  return (
    <div className="min-h-screen bg-[#061844] text-[#58C612] p-4 sm:p-6 lg:p-8 select-none">
      {/* Apply global styles */}
      <style dangerouslySetInnerHTML={{ __html: globalStyles }} />
      <div className="max-w-7xl mx-auto">
        <header className="mb-8">
          <img
            src="/project_parlay_logo.png"
            alt="Project Parlay Logo"
            className="mx-auto h-20 sm:h-24"
          />
        </header>

        {/* Accuracy and Capital Section */}
        <section className="mb-8 p-6 bg-gray-900 bg-opacity-75 border border-[#58C612] rounded-xl shadow-lg">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 items-end">
            <div>
              <label
                htmlFor="accuracyInput"
                className="block text-lg font-medium text-[#58C612] mb-2"
              >
                User Accuracy:{" "}
                <span className="font-semibold text-[#58C612]">
                  {fetchedAccuracy}
                </span>
              </label>
              <div className="flex items-center gap-x-3">
                <input
                  type="text"
                  id="accuracyInput"
                  value={userAccuracy}
                  onChange={(e) => setUserAccuracy(e.target.value)}
                  placeholder="Enter accuracy %"
                  className="flex-grow p-3 bg-gray-800 text-white border border-gray-700 rounded-md focus:outline-none placeholder-gray-500"
                />
                <button
                  onClick={handleUpdateAccuracy}
                  className="hover:cursor-pointer p-3 bg-[#58C612] hover:bg-[#80b560] text-black font-bold rounded-md transition-colors duration-300 whitespace-nowrap"
                >
                  Update
                </button>
              </div>
            </div>
            <div>
              <label
                htmlFor="totalCapitalInput"
                className="block text-lg font-medium text-[#58C612] mb-2"
              >
                Total Capital ($)
              </label>
              <input
                type="number"
                id="totalCapitalInput"
                value={totalCapital}
                onChange={(e) => setTotalCapital(e.target.value)}
                placeholder="Enter total bankroll"
                className="w-full p-3 bg-gray-800 text-white border border-gray-700 rounded-md focus:outline-none placeholder-gray-500"
              />
            </div>
          </div>
        </section>

        {/* Pick Input Section */}
        <section className="mb-8 p-6 bg-gray-900 bg-opacity-75 border border-[#58C612] rounded-xl shadow-lg">
          <h4 className="text-xl font-semibold text-[#58C612] mb-3">
            Enter Pick Name (eg. Mike Trout 0.5 HR)
          </h4>
          <input
            type="text"
            id="pickNameInput"
            value={pickNameInput}
            onChange={(e) => setPickNameInput(e.target.value)}
            placeholder="Enter name here"
            className="w-full p-3 bg-gray-800 text-white border border-gray-700 rounded-md focus:outline-none placeholder-gray-500"
          />
        </section>

        {/* Button Stack */}
        <section className="mb-8 p-6 bg-gray-900 bg-opacity-75 border border-[#58C612] rounded-xl shadow-lg">
          <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            <button
              onClick={handleOpenPickForm}
              className="p-3 hover:cursor-pointer bg-[#ffda00] hover:bg-[#ccac00] text-black font-bold rounded-md transition-colors"
            >
              Create Pick
            </button>
            <button
              onClick={handleOpenBoostForm}
              className="p-3 hover:cursor-pointer bg-[#ffda00] hover:bg-[#ccac00] text-black font-bold rounded-md transition-colors"
            >
              New Boost Promo
            </button>
            <button
              onClick={handleOpenProtectedForm}
              className="p-3 hover:cursor-pointer bg-[#ffda00] hover:bg-[#ccac00] text-black font-bold rounded-md transition-colors"
            >
              New Protected Promo
            </button>
            <button
              onClick={handleRunOptimizer}
              className="p-3 hover:cursor-pointer bg-[#ffda00] hover:bg-[#ccac00] text-black font-bold rounded-md transition-colors"
            >
              Run Optimizer
            </button>
            <button
              onClick={handleLoadSamplePicks}
              className="p-3 hover:cursor-pointer bg-[#ffda00] hover:bg-[#ccac00] text-black font-bold rounded-md transition-colors"
            >
              Load Sample Picks
            </button>
            <button
              onClick={handleClearPicks}
              className="p-3 hover:cursor-pointer bg-red-500 hover:bg-red-700 text-white font-bold rounded-md transition-colors"
            >
              Clear All Picks
            </button>
            <button
              onClick={handleSubmitVerifiedPicks}
              className="p-3 hover:cursor-pointer bg-[#ffda00] hover:bg-[#ccac00] text-black font-bold rounded-md transition-colors col-span-2 sm:col-span-1 md:col-span-1 lg:col-span-2"
            >
              Submit Verified Picks
            </button>
          </div>
        </section>

        {/* Server Response & Optimizer Result */}
        {(serverResponse || optimizerResultSummary) && (
          <section className="mb-8 p-6 bg-gray-800 bg-opacity-80 border border-[#58C612] rounded-xl shadow-md">
            {serverResponse && (
              <p className="text-[#58C612] mb-2">
                <strong className="text-green-200">Server Response:</strong>{" "}
                {serverResponse}
              </p>
            )}
            {optimizerResultSummary && (
              <p className="text-[#58C612]">
                <strong className="text-green-200">Optimizer Result:</strong>{" "}
                {optimizerResultSummary}
              </p>
            )}
          </section>
        )}

        {/* Subparlays Grid */}
        {subparlays.length > 0 && (
          <section className="mb-8 p-6 bg-gray-900 bg-opacity-75 border border-[#58C612] rounded-xl shadow-lg">
            <h3 className="text-2xl font-semibold text-[#58C612] mb-4">
              Generated Subparlays
            </h3>
            <div id="subparlayGrid" className="flex flex-wrap gap-2">
              {subparlays.map((column, i) => {
                let topLabelText = "Standard";
                if (column.length >= 3) {
                  const oddsList = column.map((p) => parseFloat(p.odds));
                  const meanOdds =
                    oddsList.reduce((a, b) => a + b, 0) / oddsList.length;
                  let votes = { Flex: 0, Standard: 0 };
                  oddsList.forEach((odds) => {
                    if (odds - meanOdds >= 0.12) votes.Flex++;
                    else if (meanOdds - odds >= 0.12) votes.Standard++;
                  });
                  if (votes.Flex > votes.Standard) topLabelText = "Flex";
                }
                return (
                  <div
                    key={i}
                    className="flex flex-col items-center p-1 bg-gray-800 rounded"
                  >
                    <div className="text-xs font-bold text-green-200 mb-1">
                      {topLabelText}
                    </div>
                    <div className="flex flex-col border-2 border-gray-700 p-0.5 w-10 space-y-0.5">
                      {column.map((pick) => (
                        <div
                          key={pick.pID}
                          title={`ID: ${pick.pID}, Odds: ${
                            pick.odds
                          }, Confidence: ${pick.confidence?.toFixed(2)}%`}
                          className="w-full h-5 text-xxs flex items-center justify-center text-white font-mono"
                          style={{
                            backgroundColor: getConfidenceColor(
                              pick.confidence
                            ),
                          }}
                        >
                          {`#${pick.pID}`}
                        </div>
                      ))}
                    </div>
                    {/* Bottom label if needed, from MVP: "None" */}
                  </div>
                );
              })}
            </div>
          </section>
        )}

        {/* Current Picks Display */}
        <section className="p-6 bg-gray-900 bg-opacity-75 border border-[#58C612] rounded-xl shadow-lg">
          <h3 className="text-2xl font-semibold text-[#58C612] mb-6">
            Current Picks
          </h3>
          <div id="objectButtonsContainer" className="space-y-5">
            {picks.map((pick, index) => (
              <React.Fragment key={`pick-${pick.id}-${index}`}>
                {optimizerSplitIndex !== undefined &&
                  index === optimizerSplitIndex && (
                    <div className="border-t-4 border-red-500 my-4"></div>
                  )}
                <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 p-4 rounded-lg shadow-md">
                  <div
                    className="flex-grow text-white p-3 rounded-md min-w-0"
                    style={{
                      backgroundColor: getConfidenceColor(pick.confidence),
                    }}
                  >
                    <div
                      className="font-bold text-lg truncate"
                      title={pick.name}
                    >
                      [{pick.pID || pick.id}] {pick.name}
                    </div>
                    <div className="text-xs sm:text-sm">
                      Odds: {pick.odds} | &alpha; Model:{" "}
                      {pick.bayesian_prob || "N/A"} | &beta; Model:{" "}
                      {pick.logistic_prob || "N/A"} &rarr;{" "}
                      <b>
                        Score:{" "}
                        {pick.confidence !== undefined
                          ? pick.confidence.toFixed(2)
                          : "N/A"}
                        %
                      </b>
                    </div>
                    {pick.bayesian_conf !== undefined && (
                      <div className="mt-2 h-2 w-full flex border border-gray-600 rounded overflow-hidden">
                        <div
                          style={{
                            width: `${pick.bayesian_conf * 100}%`,
                            backgroundColor: "purple",
                          }}
                          title={`Bayesian Influence: ${(
                            pick.bayesian_conf * 100
                          ).toFixed(0)}%`}
                        ></div>
                        <div
                          style={{
                            width: `${(1 - pick.bayesian_conf) * 100}%`,
                            backgroundColor: "gold",
                          }}
                          title={`Logistic Influence: ${(
                            (1 - pick.bayesian_conf) *
                            100
                          ).toFixed(0)}%`}
                        ></div>
                      </div>
                    )}
                  </div>
                  <div className="flex flex-col sm:flex-row sm:items-center gap-3 shrink-0 mt-3 sm:mt-0 w-full sm:w-auto">
                    <button
                      onClick={() => handleEditPick(pick)}
                      className="py-2 px-4 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-md text-sm ring-1 ring-black shadow-md hover:shadow-lg hover:-translate-y-px transition-all duration-200 ease-linear w-full sm:w-24 flex-1 h-10 hover:cursor-pointer"
                    >
                      Edit
                    </button>
                    <button
                      onClick={() => handleDeletePick(pick.id)}
                      className="py-2 px-4 bg-red-700 hover:bg-red-800 text-white font-semibold rounded-md text-sm ring-1 ring-black shadow-md hover:shadow-lg hover:-translate-y-px transition-all duration-200 ease-linear w-full sm:w-24 flex-1 h-10 hover:cursor-pointer"
                    >
                      Delete
                    </button>
                    <button
                      onClick={() => togglePickVerification(pick.id)}
                      className="py-2 px-4 bg-purple-600 hover:bg-purple-700 text-white font-semibold rounded-md text-sm ring-1 ring-black shadow-md hover:shadow-lg hover:-translate-y-px transition-all duration-200 ease-linear w-full sm:w-24 flex-1 h-10 hover:cursor-pointer"
                    >
                      Verify: {pick.is_verified_temp_state}
                    </button>
                  </div>
                </div>
              </React.Fragment>
            ))}
            {picks.length === 0 && (
              <p className="text-center text-gray-400">No picks available.</p>
            )}
          </div>
        </section>
      </div>

      {/* Modal Forms */}
      <PickForm
        isOpen={showPickForm}
        onClose={handleClosePickForm}
        onSubmit={handleSubmitPickForm}
        initialName={tempPickName}
        editingPick={editingPick}
      />
      <BoostForm
        isOpen={showBoostForm}
        onClose={handleCloseBoostForm}
        onSubmit={handleSubmitBoostForm}
      />
      <ProtectedForm
        isOpen={showProtectedForm}
        onClose={handleCloseProtectedForm}
        onSubmit={handleSubmitProtectedForm}
      />
    </div>
  );
}

export default ProjectParlayDashboard;
