import scrollbar from 'tailwind-scrollbar';

/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}", // Scan all relevant files in the src directory
  ],
  theme: {
    extend: {
      // You can extend the default theme here
      // For screen sizes, Tailwind has a good set of defaults (sm, md, lg, xl, 2xl)
      // which are mobile-first.
      // If you need custom screen sizes, you can define them like this:
      // screens: {
      //   'xs': '475px',
      //   ...defaultTheme.screens, // to keep default screens
      // },
    },
  },
  plugins: [
    scrollbar,
  ],
}