export interface EventData {
  event_id: string;
  event_date: string;
  player_name: string;
  event_team: string;
  handicappers: string[];
  stat_thresholds: number[];
  predictions: number[];
}

export interface TodaysEventsResponse {
  success: boolean;
  message: string;
  events: EventData[];
  date: string;
}

export const fetchTodaysEvents = async (customDate?: string): Promise<TodaysEventsResponse> => {
  try {
    const url = customDate 
      ? `/api/todays_events?date=${customDate}` 
      : "/api/todays_events";
    
    console.log("🌐 API Request URL:", url);
    console.log("📅 Custom date parameter:", customDate || "none (using current date)");
    
    const response = await fetch(url);
    console.log("📡 HTTP Response status:", response.status, response.statusText);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    console.log("🔄 Raw API Response:", JSON.stringify(data, null, 2));
    
    return data;
  } catch (error) {
    console.error("💥 Error fetching today's events:", error);
    throw error;
  }
};
