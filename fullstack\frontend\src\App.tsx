import React from "react";
import HomePage from "./pages/HomePage/HomePage.tsx";
import Login from "./pages/Login/Login.tsx";
import { useAuth } from "./contexts/AuthContext.tsx";
import AddPicksPage from "./pages/AddPicksPage/AddPicksPage.tsx";

function App() {
  const { currentView, login } = useAuth();

  switch (currentView) {
    case "login":
      return <Login performLogin={login} />;
    case "home":
      return <HomePage />;
    case "addPicks":
      return <AddPicksPage />;
    default:
      return <Login performLogin={login} />;
  }
}

export default App;
