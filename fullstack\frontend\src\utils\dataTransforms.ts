import { EventData } from "./api";

// Type for the Pick format expected by PicksView
export interface Pick {
  id: number;
  playerName: string;
  playerNumber: string;
  betType: string;
  gameInfo: string;
  confidence: number;
  expertCount: number;
  additionalExperts: number;
  handicapperNames: string[]; // Add handicapper names for expanded view
}

// Helper function to extract player name from event_id
export const extractPlayerName = (eventId: string): string => {
  // Parse event_id pattern: "2025-05-28-NBA-KTOWNS14.0PTS"
  const parts = eventId.split('-');
  
  if (parts.length >= 5) {
    const playerPart = parts[4]; // "KTOWNS14.0PTS"
    // Remove the stat part (numbers + letters at the end)
    const playerName = playerPart.replace(/\d+(?:\.\d+)?[A-Z]+$/, '');
    
    // Format common player name patterns
    if (playerName === 'KTOWNS') return 'K. Towns';
    if (playerName === 'THALIBURTON') return 'T. Haliburton';
    if (playerName === 'JBRUNSON') return '<PERSON><PERSON> <PERSON>';
    if (playerName === 'TESTPLAYER') return 'Test Player';
    
    // Default: just return the extracted name
    return playerName;
  }
  
  return "Unknown Player";
};

// Helper function to extract player number from event_id or return default
export const extractPlayerNumber = (eventId: string, playerName: string): string => {
  // Return jersey numbers for known players
  const name = extractPlayerName(eventId);
  if (name === 'K. Towns') return '32';
  if (name === 'T. Haliburton') return '0';  // Tyrese Haliburton actually wears #0
  if (name === 'J. Brunson') return '11';
  if (name === 'Test Player') return '99';
  
  // Default fallback
  return "0";
};

// Helper function to format bet type from stat_thresholds and predictions
export const formatBetType = (event: EventData): string => {
  if (event.stat_thresholds.length > 0 && event.predictions.length > 0) {
    const threshold = event.stat_thresholds[0];
    
    // Handle null thresholds
    if (threshold === null || threshold === undefined) {
      return "Standard Bet";
    }
    
    // Calculate majority prediction
    const overCount = event.predictions.filter(p => p === 1).length;
    const underCount = event.predictions.filter(p => p === 0).length;
    const direction = overCount > underCount ? "Over" : "Under";
    
    // Extract stat type from event_id (e.g., "PTS", "AST", "REB")
    const statMatch = event.event_id.match(/([A-Z]+)$/);
    let statType = "Points";
    if (statMatch) {
      const stat = statMatch[1];
      if (stat === "PTS") statType = "Points";
      else if (stat === "AST") statType = "Assists";
      else if (stat === "REB") statType = "Rebounds";
      else statType = stat;
    }
    
    return `${direction} ${threshold} ${statType}`;
  }
  return "Standard Bet";
};

// Helper function to format game info
export const formatGameInfo = (event: EventData): string => {
  // Extract date from event_id since event_date is null
  const dateParts = event.event_id.split('-');
  let dateStr = "Today";
  let timeStr = "8:00 pm";
  
  if (dateParts.length >= 3) {
    try {
      const date = new Date(`${dateParts[0]}-${dateParts[1]}-${dateParts[2]}`);
      timeStr = date.toLocaleTimeString('en-US', { 
        hour: 'numeric', 
        minute: '2-digit',
        hour12: true 
      });
      const dayStr = date.toLocaleDateString('en-US', { weekday: 'short' });
      dateStr = `${dayStr}, ${timeStr}`;
    } catch (e) {
      // Fallback if date parsing fails
      dateStr = "Today, 8:00 pm";
    }
  }
  
  // Extract sport from event_id (e.g., "NBA") - it's at index 3, not index 4
  const sport = dateParts.length >= 4 ? dateParts[3] : "NBA";
  
  // For now, use placeholder team info since event_team is null
  const playerName = extractPlayerName(event.event_id);
  return `${sport} | ${playerName} | ${dateStr}`;
};

// Helper function to calculate confidence based on predictions
export const calculateConfidence = (predictions: number[]): number => {
  if (predictions.length === 0) return 50;
  
  const agreementCount = predictions.filter(p => p === predictions[0]).length;
  const agreementRatio = agreementCount / predictions.length;
  
  // Convert agreement ratio to confidence percentage (50-95 range)
  return Math.round(50 + (agreementRatio * 45));
};

// Main conversion function to convert EventData to Pick format expected by PicksView
export const convertEventToPick = (event: EventData, index: number): Pick => {
  const playerName = extractPlayerName(event.event_id);
  const playerNumber = extractPlayerNumber(event.event_id, playerName);
  const betType = formatBetType(event);
  const gameInfo = formatGameInfo(event);
  const confidence = calculateConfidence(event.predictions);
  
  return {
    id: index + 1,
    playerName: playerName,
    playerNumber: playerNumber,
    betType: betType,
    gameInfo: gameInfo,
    confidence: confidence,
    expertCount: Math.min(event.handicappers.length, 9), // Cap at 9 for display
    additionalExperts: Math.max(0, event.handicappers.length - 9),
    handicapperNames: event.handicappers, // Include all handicapper names
  };
};

// Function to convert multiple events to picks
export const convertEventsToPicks = (events: EventData[]): Pick[] => {
  return events.map((event, index) => convertEventToPick(event, index));
};

// Interface for Handicapper data format expected by HandicappersView
export interface HandicapperPick {
  id: number;
  playerName: string;
  playerNumber: string;
  betType: string;
  gameInfo: string;
  confidence?: number;
}

export interface Handicapper {
  id: number;
  name: string;
  sports: string;
  rating: number;
  accuracy: string;
  profileImage: string;
  picks: HandicapperPick[];
}

// Function to convert events to handicappers format
export const convertEventsToHandicappers = (events: EventData[]): Handicapper[] => {
  if (events.length === 0) {
    return [];
  }

  // Group events by handicapper names
  const handicapperMap = new Map<string, EventData[]>();
  
  events.forEach(event => {
    event.handicappers.forEach(handicapperName => {
      if (!handicapperMap.has(handicapperName)) {
        handicapperMap.set(handicapperName, []);
      }
      handicapperMap.get(handicapperName)!.push(event);
    });
  });

  // Convert to handicapper format
  const handicappers: Handicapper[] = [];
  let handicapperId = 1;

  handicapperMap.forEach((handicapperEvents, handicapperName) => {
    // Calculate accuracy based on prediction consistency (mock logic)
    const totalPredictions = handicapperEvents.reduce((sum, event) => sum + event.predictions.length, 0);
    const accuracy = Math.min(95, 70 + Math.floor(totalPredictions * 2)); // Mock accuracy calculation
    
    // Create picks for this handicapper
    const picks: HandicapperPick[] = handicapperEvents.map((event, index) => {
      const playerName = extractPlayerName(event.event_id);
      const playerNumber = extractPlayerNumber(event.event_id, playerName);
      const betType = formatBetType(event);
      const gameInfo = formatGameInfo(event);
      const confidence = calculateConfidence(event.predictions);
      
      return {
        id: index + 1,
        playerName,
        playerNumber,
        betType,
        gameInfo,
        confidence,
      };
    });

    handicappers.push({
      id: handicapperId++,
      name: handicapperName,
      sports: "NBA, NFL, NHL", // Default sports
      rating: Math.min(5, Math.max(3, Math.floor(accuracy / 20))), // Convert accuracy to 3-5 star rating
      accuracy: `${accuracy}%`,
      profileImage: `/profile-${handicapperName.toLowerCase().replace(/\s+/g, '-')}.jpg`,
      picks,
    });
  });

  return handicappers;
};
