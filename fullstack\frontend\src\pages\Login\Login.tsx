import React, { useState, useEffect } from 'react';
// import { useNavigate } from 'react-router-dom'; // Removed useNavigate
import StartupScreen from '../../components/StartupScreen/StartupScreen';

const DEV_MODE = false; 
const SIMULATED_LOAD_TIME = DEV_MODE ? Math.round(100 / 1.5) : Math.round(3000 / 1.5); // Simulate 1.5x faster loading (approx 2s or 67ms in dev)

interface LoginProps {
  performLogin: () => void;
}

function Login({ performLogin }: LoginProps) {
  const [isAppLoading, setIsAppLoading] = useState(true); // Controls the StartupScreen visibility
  const [hasStartupScreenCompleted, setHasStartupScreenCompleted] = useState(DEV_MODE); // True when StartupScreen calls onAnimationComplete
  const [animateTitle, setAnimateTitle] = useState(DEV_MODE);
  const [showLoginForm, setShowLoginForm] = useState(DEV_MODE);
  // const navigate = useNavigate(); // Removed navigate

  // Simulate app loading completion
  useEffect(() => {
    document.title = 'Project Parlay - Loading...';
    const timer = setTimeout(() => {
      setIsAppLoading(false); // Signal that background loading is done
    }, SIMULATED_LOAD_TIME);

    return () => clearTimeout(timer);
  }, []);

  // This is called when the StartupScreen's fade-out animation finishes
  const handleActualLoadingAnimationEnd = () => {
    setHasStartupScreenCompleted(true);
    document.title = DEV_MODE ? 'Project Parlay - Login (Dev)' : 'Project Parlay - Login';
  };

  // Start title animation after loading screen is gone (or immediately in DEV_MODE)
  useEffect(() => {
    if (DEV_MODE) {
      setAnimateTitle(true);
      return;
    }
    if (hasStartupScreenCompleted) {
      const titleTimer = setTimeout(() => {
        setAnimateTitle(true);
      }, 250); // Small delay before title animates in
      return () => clearTimeout(titleTimer);
    }
  }, [hasStartupScreenCompleted]);

  // Show login form after title animation starts (or immediately in DEV_MODE)
  useEffect(() => {
    if (DEV_MODE) {
      setShowLoginForm(true);
      return;
    }
    if (animateTitle) {
      const formTimer = setTimeout(() => {
        setShowLoginForm(true);
      }, 450); // Delay for title animation to have an effect
      return () => clearTimeout(formTimer);
    }
  }, [animateTitle]);

  const handleLoginSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    console.log("Login form submitted. Calling performLogin...");
    performLogin();
  };

  return (
    <div className="min-h-screen bg-[#061844] flex flex-col items-center justify-center relative overflow-hidden p-4">
      <StartupScreen 
        videoSrc="/project_parlay_startup_anim.mp4"
        isLoading={isAppLoading}
        onAnimationComplete={handleActualLoadingAnimationEnd} 
      />

      {/* Container for Title and Form, only visible after loading screen is done and animation states are ready */}
      {hasStartupScreenCompleted && (
        <div className="w-full max-h-[calc(100vh-2rem)] flex flex-col items-center overflow-y-auto px-4 pt-8 pb-4">
          {/* <h1 
            className={`
              text-[#58C612] text-6xl select-none sm:text-6xl font-bold
              transition-all duration-1000 ease-in-out 
              ${animateTitle ? '-translate-y-[2.5vh] sm:-translate-y-[2.5vh] opacity-100' : 'translate-y-0 opacity-0'}
            `}
          >
            ProjectParlay
          </h1> */}
          <img 
            src="/project_parlay_logo.png" 
            alt="Project Parlay Logo" 
            className={`
              w-auto h-60 sm:h-80
              select-none
              transition-all duration-1000 ease-in-out 
              ${animateTitle ? '-translate-y-[2.5vh] sm:-translate-y-[2.5vh] opacity-100' : 'translate-y-0 opacity-0'}
            `}
          />
          
          <div 
            className={`w-full max-w-md transition-opacity duration-700 ease-in-out ${showLoginForm ? 'opacity-100' : 'opacity-0 pointer-events-none'}`}
          >
            <form 
              onSubmit={handleLoginSubmit} 
              className="bg-transparent border-2 border-[#58C612] rounded-xl shadow-2xl shadow-[#58C612]/50 p-6 sm:p-8 space-y-6 "
            >
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-green-300 mb-1 select-none">Email</label>
                <input 
                  type="text" 
                  id="email"
                  placeholder="<EMAIL>" 
                  className="w-full p-3 bg-gray-900 text-white border border-gray-700 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 placeholder-gray-500 text-base sm:text-lg"
                />
              </div>
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-green-300 mb-1 select-none">Password</label>
                <input 
                  type="password" 
                  id="password"
                  placeholder="••••••••" 
                  className="w-full p-3 bg-gray-900 text-white border border-gray-700 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 placeholder-gray-500 text-base sm:text-lg"
                />
              </div>
              <button 
                type="submit" 
                className="w-full p-3 bg-[#58C612] hover:bg-[#061844] hover:text-[#58C612] border-2 border-black hover:border-2 hover:border-[#58C612] text-black font-bold text-base sm:text-lg rounded-md transition-all ease-linear duration-300 hover:cursor-pointer"
              >
                Login
              </button>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}

export default Login; 