import React, { useState, useRef, useEffect } from 'react';

interface LoadingScreenProps {
  onAnimationComplete: () => void;
  videoSrc: string;
  isLoading: boolean;
  loadingText?: string;
  subText?: string;
}

const LoadingScreen: React.FC<LoadingScreenProps> = ({ onAnimationComplete, videoSrc, isLoading, loadingText, subText }) => {
  const [internalShow, setInternalShow] = useState(isLoading); // Internal state to manage actual rendering
  const [isFadingOut, setIsFadingOut] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    if (!isLoading && internalShow) { // Start fade out if isLoading becomes false and it's currently shown
      setIsFadingOut(true);
      setTimeout(() => {
        setInternalShow(false); // Actually hide after fade
        onAnimationComplete();
      }, 800); // Duration of the fade-out animation
    } else if (isLoading && !internalShow) { // If needs to be shown (again)
      setIsFadingOut(false); // Ensure not fading out
      setInternalShow(true); // Show it
    }
  }, [isLoading, internalShow, onAnimationComplete]);

  const handleVideoError = () => {
    console.error("Video playback error.");
    // Fallback: immediately call onAnimationComplete if video fails, attempt fade
    setIsFadingOut(true);
    setTimeout(() => {
      setInternalShow(false);
      onAnimationComplete();
    }, 800);
  };

  useEffect(() => {
    // Autoplay when the component is intended to be visible and not fading out
    if (internalShow && !isFadingOut && videoRef.current) {
      videoRef.current.play().catch(error => {
        // If play() is interrupted by a new command or similar, it might reject.
        // We only care about critical errors for handleVideoError.
        // For now, log non-critical ones if needed, or ignore.
        if (error.name !== 'AbortError') {
          handleVideoError();
        }
      });
    }
  }, [internalShow, isFadingOut]); // Re-run play logic if visibility or fading state changes

  if (!internalShow && !isFadingOut) { // Don't render if fully hidden and not in process of fading out
    return null;
  }  return (
    <div
      className={`fixed inset-0 z-[10000] flex items-center justify-center bg-black transition-opacity duration-800 ease-in-out ${isFadingOut ? 'opacity-0 pointer-events-none' : 'opacity-100'}`}
    >
      <div className="relative w-[60vw] max-w-[800px] aspect-video flex items-center justify-center overflow-hidden">
        <video
          ref={videoRef}
          src={videoSrc}
          muted
          playsInline
          loop // Video loops
          onError={handleVideoError}
          className="w-full h-full object-contain block"        />
      </div>
        {/* Loading text overlay - positioned relative to full viewport */}
      {(loadingText || subText) && (
        <div className="fixed bottom-[15%] left-1/2 transform -translate-x-1/2 text-center z-[10001]">
          {loadingText && (
            <p className="text-white text-xl font-medium mb-4">{loadingText}</p>
          )}
          {subText && (
            <p className="text-blue-300 text-sm">{subText}</p>
          )}
        </div>
      )}
    </div>
  );
};

export default LoadingScreen; 