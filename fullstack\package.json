{"name": "fullstack", "version": "1.0.0", "main": "index.js", "scripts": {"setup": "npm --prefix frontend install && pip install -r backend/requirements.txt", "dev": "concurrently \"npm --prefix frontend run dev\" \"python backend/app.py\"", "preview": "npm --prefix frontend run build && python backend/app.py"}, "author": "", "license": "ISC", "description": "", "devDependencies": {"concurrently": "^9.1.2"}, "dependencies": {"dotenv": "^16.5.0"}}