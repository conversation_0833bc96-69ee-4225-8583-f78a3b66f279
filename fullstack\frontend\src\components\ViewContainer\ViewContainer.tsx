import React, { useState } from "react";
import Header from "../Header";
import PicksView from "../PicksView";
import HandicappersView from "../HandicappersView";

type ViewType = "picks" | "handicappers";

interface Pick {
  id: number;
  playerName: string;
  playerNumber: string;
  betType: string;
  gameInfo: string;
  confidence: number;
  expertCount: number;
  additionalExperts: number;
  handicapperNames: string[];
}

interface HandicapperPick {
  id: number;
  playerName: string;
  playerNumber: string;
  betType: string;
  gameInfo: string;
}

interface Handicapper {
  id: number;
  name: string;
  sports: string;
  rating: number;
  accuracy: string;
  profileImage: string;
  picks: HandicapperPick[];
}

interface ViewContainerProps {
  onBackToHome: () => void;
  picks: Pick[];
  handicappers: Handicapper[];
}

function ViewContainer({
  onBackToHome,
  picks,
  handicappers,
}: ViewContainerProps) {
  const [activeView, setActiveView] = useState<ViewType>("picks");

  return (
    <div className="min-h-screen bg-[#061844] text-white select-none">
      <Header onBackToHome={onBackToHome} />

      <div className="mx-auto px-4 py-4">
        <div className="flex items-center gap-4">
          <div className="mx-auto flex flex-col sm:flex-row items-center justify-center gap-4">
            <span className="text-white text-lg sm:text-xl md:text-[24px]">View By</span>
            <div className="flex rounded-lg overflow-hidden border-[#233e6c] border-4 ">
              <button
                onClick={() => setActiveView("picks")}
                className={`px-4 sm:px-6 py-2 text-sm sm:text-base font-semibold hover:cursor-pointer transition-colors ${
                  activeView === "picks"
                    ? "bg-[#233e6c] text-white"
                    : "text-gray-400 hover:text-white"
                }`}
              >
                Picks
              </button>
              <button
                onClick={() => setActiveView("handicappers")}
                className={`px-4 sm:px-6 py-2 text-sm sm:text-base font-semibold hover:cursor-pointer transition-colors ${
                  activeView === "handicappers"
                    ? "bg-[#233e6c] text-white"
                    : "text-gray-400 hover:text-white"
                }`}
              >
                Handicappers
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 pb-8">
        {activeView === "picks" ? (
          <PicksView picks={picks} />
        ) : (
          <HandicappersView handicappers={handicappers} />
        )}
      </div>
    </div>
  );
}

export default ViewContainer;
