import React, { useState, useRef, useEffect } from 'react';

interface LoadingScreenProps {
  onAnimationComplete: () => void;
  videoSrc: string;
  isLoading: boolean;
  loadingText?: string;
  subText?: string;
}

const LoadingScreen: React.FC<LoadingScreenProps> = ({ onAnimationComplete, videoSrc, isLoading, loadingText, subText }) => {
  const [internalShow, setInternalShow] = useState(isLoading); // Internal state to manage actual rendering
  const [isFadingOut, setIsFadingOut] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    if (!isLoading && internalShow) { // Start fade out if isLoading becomes false and it's currently shown
      setIsFadingOut(true);
      setTimeout(() => {
        setInternalShow(false); // Actually hide after fade
        onAnimationComplete();
      }, 800); // Duration of the fade-out animation
    } else if (isLoading && !internalShow) { // If needs to be shown (again)
      setIsFadingOut(false); // Ensure not fading out
      setInternalShow(true); // Show it
    }
  }, [isLoading, internalShow, onAnimationComplete]);

  const handleVideoError = () => {
    console.error("Video playback error.");
    // Fallback: immediately call onAnimationComplete if video fails, attempt fade
    setIsFadingOut(true);
    setTimeout(() => {
      setInternalShow(false);
      onAnimationComplete();
    }, 800);
  };

  useEffect(() => {
    // Autoplay and set playback rate when the component is intended to be visible and not fading out
    if (internalShow && !isFadingOut && videoRef.current) {
      videoRef.current.playbackRate = 1.5; // Set playback rate to 1.5x
      videoRef.current.play().catch(error => {
        // If play() is interrupted by a new command or similar, it might reject.
        // We only care about critical errors for handleVideoError.
        // For now, log non-critical ones if needed, or ignore.
        if (error.name !== 'AbortError') {
          handleVideoError();
        }
      });
    }
  }, [internalShow, isFadingOut]); // Re-run play logic if visibility or fading state changes

  if (!internalShow && !isFadingOut) { // Don't render if fully hidden and not in process of fading out
    return null;
  }
  return (
    <div
      className={`fixed inset-0 z-[10000] flex items-center justify-center bg-[#061844] transition-opacity duration-800 ease-in-out ${isFadingOut ? 'opacity-0 pointer-events-none' : 'opacity-100'}`}
    >
      <div className="relative w-[100vw] h-[100vh] aspect-video flex items-center justify-center overflow-hidden">
        <video
          ref={videoRef}
          src={videoSrc}
          muted
          playsInline
          loop
          onError={handleVideoError}
          className="w-full h-full object-contain block"
        />
        
        {/* Loading text overlay */}
        {(loadingText || subText) && (
          <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2 text-center">
            {loadingText && (
              <div className="flex items-center justify-center mb-4">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mr-3"></div>
                <p className="text-white text-xl font-medium">{loadingText}</p>
              </div>
            )}
            {subText && (
              <p className="text-blue-300 text-sm">{subText}</p>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default LoadingScreen; 